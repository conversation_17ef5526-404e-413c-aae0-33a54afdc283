"""
Stage 8: Script Consolidation and Optimization

This module handles Phase 8 of the GretahAI ScriptWeaver application workflow.
Stage 8 is responsible for taking the final merged script from Stage 7
and optimizing it using Google AI to create a well-structured, cohesive PyTest module
following best practices.

Key Features:
- **Final Script Optimization**: Works exclusively with the final merged script from Stage 7
- Google AI API integration for script optimization via optimize_script_with_ai in core/ai.py
- Chunked optimization approach for large scripts
- Detailed optimization metrics and comparison
- AI-powered validation of optimized scripts
- Proper handling of optimization results (success/failure scenarios)
- Manual navigation options (Stage 8 → Stage 9 for script browsing or Stage 8 → Stage 3 for new test case selection)
- Download functionality for optimized scripts
- Enhanced script testing capabilities

Script Selection Logic:
The optimization process works exclusively with the final merged script from Stage 7.
This eliminates redundant merging logic and ensures a streamlined optimization workflow.

The stage maintains the established architectural patterns:
- StateManager pattern with logging and st.rerun() after state changes
- Collapsible sections using st.expander() with expanded=True
- Manual progression with confirmation buttons
- Minimalist UI design with minimal instructional text
- Proper workflow transitions back to Stage 3

Functions:
    stage8_optimize_script(state): Main Stage 8 function for script optimization
    execute_test_script(script_path, test_context): Execute and validate optimized scripts
"""

import os
import logging
import streamlit as st
from datetime import datetime
import time
import re
import subprocess
from pathlib import Path

# Import AI functions for script optimization and validation
from core.ai import optimize_script_with_ai, validate_generated_script, enhance_user_comment_with_ai

# Import helper functions from stage6 (if needed for future functionality)
# from stages.stage6 import create_final_script_with_header

# Import state management
from state_manager import StateStage

# Configure logging
logger = logging.getLogger("ScriptWeaver.stage8")


def _create_debug_log_file():
    """
    Create a timestamped debug log file for Stage 8 script debugging.

    Returns:
        str: Path to the created log file, or None if creation failed
    """
    try:
        # Create debug_logs directory
        debug_dir = "debug_logs"
        os.makedirs(debug_dir, exist_ok=True)

        # Create timestamped filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = f"stage8_script_debug_{timestamp}.log"
        log_path = os.path.join(debug_dir, log_filename)

        # Create the file with initial header
        with open(log_path, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("STAGE 8 SCRIPT DEBUG LOG\n")
            f.write("=" * 80 + "\n")
            f.write(f"Created: {datetime.now().isoformat()}\n")
            f.write(f"Purpose: Track state.combined_script_path and state.combined_script_content usage\n")
            f.write("=" * 80 + "\n\n")

        logger.info(f"Stage 8: Created debug log file: {log_path}")
        return log_path

    except Exception as e:
        logger.error(f"Stage 8: Failed to create debug log file: {e}")
        return None


def _log_script_state_debug(state, context: str, debug_log_path: str = None, error_info: str = None):
    """
    Log comprehensive information about script state variables to debug file.

    Args:
        state: StateManager instance
        context: Description of when/where this logging is happening
        debug_log_path: Path to debug log file (if None, creates new one)
        error_info: Additional error information to log
    """
    try:
        # Create debug log file if not provided
        if debug_log_path is None:
            debug_log_path = _create_debug_log_file()
            if debug_log_path is None:
                return  # Failed to create log file

        # Gather information about script state
        timestamp = datetime.now().isoformat()

        # Check combined_script_path
        script_path = getattr(state, 'combined_script_path', None)
        path_exists = False
        path_readable = False
        file_size = 0
        file_content_preview = None

        if script_path:
            path_exists = os.path.exists(script_path)
            if path_exists:
                try:
                    with open(script_path, 'r', encoding='utf-8') as f:
                        file_content = f.read()
                        file_size = len(file_content)
                        file_content_preview = file_content[:200] if file_content else ""
                    path_readable = True
                except Exception as e:
                    path_readable = False
                    file_content_preview = f"ERROR READING FILE: {e}"

        # Check combined_script_content
        script_content = getattr(state, 'combined_script_content', None)
        content_length = len(script_content) if script_content else 0
        content_preview = script_content[:200] if script_content else ""

        # Determine synchronization status
        sync_status = "UNKNOWN"
        if script_path and script_content and path_readable:
            try:
                with open(script_path, 'r', encoding='utf-8') as f:
                    file_content = f.read()
                if file_content == script_content:
                    sync_status = "SYNCHRONIZED"
                else:
                    sync_status = "DESYNCHRONIZED"
            except Exception:
                sync_status = "SYNC_CHECK_FAILED"
        elif script_path and not script_content:
            sync_status = "PATH_ONLY"
        elif script_content and not script_path:
            sync_status = "CONTENT_ONLY"
        elif not script_path and not script_content:
            sync_status = "BOTH_MISSING"

        # Write to debug log
        with open(debug_log_path, 'a', encoding='utf-8') as f:
            f.write(f"\n{'='*60}\n")
            f.write(f"CONTEXT: {context}\n")
            f.write(f"TIMESTAMP: {timestamp}\n")
            f.write(f"{'='*60}\n")

            # Script path information
            f.write(f"\nSCRIPT PATH ANALYSIS:\n")
            f.write(f"  combined_script_path: {script_path}\n")
            f.write(f"  Path exists: {path_exists}\n")
            f.write(f"  Path readable: {path_readable}\n")
            f.write(f"  File size: {file_size} characters\n")

            # Script content information
            f.write(f"\nSCRIPT CONTENT ANALYSIS:\n")
            f.write(f"  combined_script_content length: {content_length} characters\n")
            f.write(f"  Content available: {script_content is not None}\n")

            # Synchronization status
            f.write(f"\nSYNCHRONIZATION STATUS: {sync_status}\n")

            # Error information
            if error_info:
                f.write(f"\nERROR INFORMATION:\n")
                f.write(f"  {error_info}\n")

            # Content previews
            if file_content_preview:
                f.write(f"\nFILE CONTENT PREVIEW (first 200 chars):\n")
                f.write(f"  {repr(file_content_preview)}\n")

            if content_preview:
                f.write(f"\nSTATE CONTENT PREVIEW (first 200 chars):\n")
                f.write(f"  {repr(content_preview)}\n")

            # Full content (if available and not too large)
            if script_content and len(script_content) < 50000:  # Limit to 50KB
                f.write(f"\n{'-'*40}\n")
                f.write(f"FULL STATE CONTENT:\n")
                f.write(f"{'-'*40}\n")
                f.write(script_content)
                f.write(f"\n{'-'*40}\n")
                f.write(f"END FULL STATE CONTENT\n")
                f.write(f"{'-'*40}\n")
            elif script_content:
                f.write(f"\nFULL CONTENT SKIPPED (too large: {len(script_content)} characters)\n")

            f.write(f"\n")

        # Also log to main logger for visibility
        logger.info(f"Stage 8 Debug: {context} - Path: {script_path}, Content: {content_length} chars, Sync: {sync_status}")

    except Exception as e:
        # Don't let logging failures break the main functionality
        logger.error(f"Stage 8: Failed to write debug log: {e}")


# Global variable to store debug log path for this session
_debug_log_path = None


def execute_test_script(script_path, test_context="optimized script"):
    """
    Execute a test script using pytest and return the results.

    This function reuses the enhanced script execution logic from Stage 7 to run
    test scripts and capture comprehensive results, performance metrics, and artifacts.

    Args:
        script_path (str): Path to the test script file
        test_context (str): Description of what's being tested (for logging/display)

    Returns:
        dict: Test execution results containing:
            - stdout: Standard output from pytest
            - stderr: Standard error from pytest
            - returncode: Exit code (0 = success, non-zero = failure)
            - success: Boolean indicating if test passed
            - xml_results: Parsed JUnit XML results
            - performance_metrics: Performance metrics from test execution
            - artifacts: Test artifacts (screenshots, logs, etc.)
            - timestamp: Execution timestamp
    """
    logger.info(f"Stage 8: Executing {test_context} at path: {script_path}")

    try:
        # Import the JUnit parser
        from core.junit_parser import parse_junit_xml, format_test_results_for_display

        # Set environment variables for the test run (same as Stage 7)
        env = os.environ.copy()
        # Always run in visible mode (not headless)
        env["HEADLESS"] = "0"
        # Use quiet mode for Stage 8 execution (less verbose by default)
        env["PYTEST_QUIET_MODE"] = "1"

        # Generate timestamped result file name
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_xml_path = f"results_stage8_{timestamp}.xml"

        # Quiet pytest command for Stage 8 (minimal console output)
        pytest_command = [
            "pytest",
            script_path,
            f"--junitxml={result_xml_path}",
            "--log-cli-level=WARNING",  # Only show warnings and errors
            "--capture=no",  # Capture output for cleaner console
            "--tb=short",  # Short traceback format
            "-q"  # Quiet mode
        ]

        logger.info(f"Stage 8: Executing pytest command: {' '.join(pytest_command)}")

        # Run the test script using enhanced pytest configuration
        result = subprocess.run(
            pytest_command,
            capture_output=True, text=True,
            env=env,
            cwd=os.getcwd()
        )

        # Parse JUnit XML results if available
        xml_results = None
        performance_metrics = {}
        artifacts = {}

        if os.path.exists(result_xml_path):
            xml_results = parse_junit_xml(result_xml_path)
            if xml_results:
                formatted_results = format_test_results_for_display(xml_results)
                performance_metrics = formatted_results.get("performance_summary", {})

                # Extract artifacts from test details
                for test_detail in formatted_results.get("test_details", []):
                    test_artifacts = test_detail.get("artifacts", {})
                    if test_artifacts:
                        artifacts.update(test_artifacts)

        # Check for screenshots (same logic as Stage 7)
        screenshots = []
        screenshots_dir = Path("screenshots")
        if screenshots_dir.exists():
            # Look for any screenshots that might have been captured
            screenshot_files = list(screenshots_dir.glob("*.png"))
            # Sort by modification time to get the most recent ones
            screenshot_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            screenshots = [str(f) for f in screenshot_files[:5]]  # Get up to 5 most recent

        # Prepare the enhanced results dictionary
        execution_results = {
            "stdout": result.stdout,
            "stderr": result.stderr,
            "returncode": result.returncode,
            "success": result.returncode == 0,
            "screenshots": screenshots,
            "script_path": script_path,
            "test_context": test_context,
            "xml_path": result_xml_path if os.path.exists(result_xml_path) else None,
            "xml_results": xml_results,
            "performance_metrics": performance_metrics,
            "artifacts": artifacts,
            "timestamp": timestamp
        }

        logger.info(f"Stage 8: {test_context} execution completed with return code: {result.returncode}")
        logger.info(f"Stage 8: Found {len(screenshots)} screenshots")
        if xml_results:
            logger.info(f"Stage 8: JUnit XML results parsed successfully")
        if performance_metrics:
            logger.info(f"Stage 8: Performance metrics collected: {len(performance_metrics)} categories")

        return execution_results

    except Exception as e:
        logger.error(f"Stage 8: Error executing {test_context}: {e}")
        import traceback
        logger.error(traceback.format_exc())

        # Return enhanced error results
        return {
            "stdout": "",
            "stderr": f"Execution error: {str(e)}",
            "returncode": -1,
            "success": False,
            "screenshots": [],
            "script_path": script_path,
            "test_context": test_context,
            "xml_path": None,
            "xml_results": None,
            "performance_metrics": {},
            "artifacts": {},
            "timestamp": datetime.now().strftime("%Y%m%d_%H%M%S"),
            "error": str(e)
        }


def stage8_optimize_script(state):
    """
    Phase 8: Script Consolidation and Optimization.

    This stage takes the combined script from Phase 7 and optimizes it using Google AI
    to create a well-structured, cohesive PyTest module following best practices.

    Args:
        state (StateManager): The application state manager instance
    """
    global _debug_log_path

    logger.info("=" * 80)
    logger.info("STAGE 8: SCRIPT OPTIMIZATION - ENTRY POINT")
    logger.info("=" * 80)

    # Initialize debug logging for this session
    if _debug_log_path is None:
        _debug_log_path = _create_debug_log_file()

    # Log initial script state
    _log_script_state_debug(state, "STAGE_8_ENTRY_POINT", _debug_log_path)

    st.markdown("<h2 class='stage-header'>Phase 8: Script Optimization</h2>", unsafe_allow_html=True)

    # Comprehensive debug information for troubleshooting
    logger.info(f"Stage 8: Entered stage8_optimize_script function")
    logger.info(f"Stage 8: Session state keys: {list(st.session_state.keys()) if hasattr(st, 'session_state') else 'N/A'}")

    # Log all optimization-related state attributes
    optimization_attrs = [
        'optimization_in_progress', 'optimization_complete', 'optimization_start_time',
        'optimized_script_path', 'optimized_script_content', 'optimization_chunks'
    ]

    for attr in optimization_attrs:
        value = getattr(state, attr, 'NOT_SET')
        if attr == 'optimization_start_time' and value != 'NOT_SET':
            logger.info(f"Stage 8: {attr} = {value} (type: {type(value)})")
        else:
            logger.info(f"Stage 8: {attr} = {value}")

    # Log script-related state attributes
    script_attrs = [
        'combined_script_content', 'combined_script_path', 'selected_test_case', 'google_api_key'
    ]

    for attr in script_attrs:
        value = getattr(state, attr, 'NOT_SET')
        if attr == 'combined_script_content' and value != 'NOT_SET' and value is not None:
            logger.info(f"Stage 8: {attr} length = {len(value)} characters")
        elif attr == 'combined_script_content' and (value == 'NOT_SET' or value is None):
            logger.info(f"Stage 8: {attr} = None or not set")
        elif attr == 'google_api_key' and value != 'NOT_SET' and value is not None:
            logger.info(f"Stage 8: {attr} present = True (length: {len(value)})")
        elif attr == 'google_api_key' and (value == 'NOT_SET' or value is None):
            logger.info(f"Stage 8: {attr} = None or not set")
        elif attr == 'selected_test_case' and value != 'NOT_SET' and value is not None:
            test_case_id = value.get('Test Case ID', 'Unknown') if isinstance(value, dict) else 'Invalid'
            logger.info(f"Stage 8: {attr} = {test_case_id}")
        else:
            logger.info(f"Stage 8: {attr} = {value}")

    logger.info(f"Stage 8: Current working directory = {os.getcwd()}")
    logger.info(f"Stage 8: Streamlit session state size = {len(st.session_state) if hasattr(st, 'session_state') else 'N/A'}")

    # Check prerequisites for optimization
    logger.info("Stage 8: Checking prerequisites for optimization")

    # Log script state before prerequisites check
    _log_script_state_debug(state, "BEFORE_PREREQUISITES_CHECK", _debug_log_path)

    prerequisites_met, missing_items = _check_optimization_prerequisites(state)

    if not prerequisites_met:
        logger.warning(f"Stage 8: Prerequisites not met. Missing items: {missing_items}")
        # Log script state when prerequisites fail
        _log_script_state_debug(state, "PREREQUISITES_FAILED", _debug_log_path,
                               error_info=f"Missing items: {missing_items}")
        _show_prerequisites_guidance(state, missing_items)
        return

    logger.info("Stage 8: All prerequisites met, proceeding with Stage 8")
    # Log script state after successful prerequisites check
    _log_script_state_debug(state, "PREREQUISITES_PASSED", _debug_log_path)

    # Display the final script from Stage 7 for optimization
    with st.expander("View Final Script for Optimization", expanded=False):
        # Log script state before attempting to display
        _log_script_state_debug(state, "BEFORE_SCRIPT_DISPLAY", _debug_log_path)

        # Load and display the final script from Stage 7
        script_content_to_display = None
        script_source_used = None

        if hasattr(state, 'combined_script_path') and state.combined_script_path and os.path.exists(state.combined_script_path):
            try:
                with open(state.combined_script_path, 'r') as f:
                    script_content_to_display = f.read()
                script_source_used = "FILE_PATH"
                st.info("📁 **Source**: Final merged script from Stage 7")
                # Log successful file read
                _log_script_state_debug(state, "SCRIPT_DISPLAY_FILE_SUCCESS", _debug_log_path,
                                       error_info=f"Successfully read from file, length: {len(script_content_to_display)}")
            except Exception as e:
                logger.error(f"Stage 8: Failed to read final script: {str(e)}")
                st.error(f"Error reading final script: {str(e)}")
                # Log file read error
                _log_script_state_debug(state, "SCRIPT_DISPLAY_FILE_ERROR", _debug_log_path,
                                       error_info=f"Failed to read file: {str(e)}")
                return
        elif hasattr(state, 'combined_script_content') and state.combined_script_content:
            script_content_to_display = state.combined_script_content
            script_source_used = "STATE_CONTENT"
            st.info("📁 **Source**: Final merged script from Stage 7")
            # Log successful content use
            _log_script_state_debug(state, "SCRIPT_DISPLAY_CONTENT_SUCCESS", _debug_log_path,
                                   error_info=f"Using state content, length: {len(script_content_to_display)}")
        else:
            script_source_used = "NONE_AVAILABLE"
            st.error("⚠️ No final script available from Stage 7. Please complete Stage 7 first.")
            # Log no script available
            _log_script_state_debug(state, "SCRIPT_DISPLAY_NO_SCRIPT", _debug_log_path,
                                   error_info="No script available from either file or state content")
            return

        # Display the script
        if script_content_to_display:
            st.code(script_content_to_display, language="python")
            st.info("📝 This is the final merged script from Stage 7 that will be optimized using Google AI.")
            # Log successful display
            _log_script_state_debug(state, "SCRIPT_DISPLAY_COMPLETE", _debug_log_path,
                                   error_info=f"Script displayed successfully, source: {script_source_used}")
        else:
            st.error("⚠️ No script content available for optimization. Please complete Stage 7 first.")
            # Log display failure
            _log_script_state_debug(state, "SCRIPT_DISPLAY_FAILED", _debug_log_path,
                                   error_info="Script content is None or empty")
            return

    # Check if optimization is already complete
    logger.info("Stage 8: Checking optimization completion status")
    logger.info(f"Stage 8: optimization_complete = {getattr(state, 'optimization_complete', 'NOT_SET')}")
    logger.info(f"Stage 8: optimized_script_path = {getattr(state, 'optimized_script_path', 'NOT_SET')}")

    if hasattr(state, 'optimization_complete') and state.optimization_complete and hasattr(state, 'optimized_script_path') and state.optimized_script_path:
        logger.info("=" * 80)
        logger.info("STAGE 8: DISPLAYING OPTIMIZATION RESULTS")
        logger.info("=" * 80)
        logger.info(f"Stage 8: Optimization is complete, displaying results")
        logger.info(f"Stage 8: Optimized script path: {state.optimized_script_path}")
        logger.info(f"Stage 8: Optimized script file exists: {os.path.exists(state.optimized_script_path)}")
        # Safe length check for optimized_script_content (handles None values)
        optimized_content = getattr(state, 'optimized_script_content', '')
        optimized_content_length = len(optimized_content) if optimized_content is not None else 0
        logger.info(f"Stage 8: Optimized script content length: {optimized_content_length}")

        st.success(f"✓ Script optimization complete: {os.path.basename(state.optimized_script_path)}")
        logger.info(f"Stage 8: Displaying completed optimization results for {state.optimized_script_path}")

        # Show enhanced optimization summary
        try:
            with open(state.optimized_script_path, 'r') as f:
                optimized_script_content = f.read()

            # Calculate statistics for the optimized script
            original_script = state.combined_script_content or ""
            original_lines = original_script.count('\n') + 1
            original_chars = len(original_script)

            optimized_lines = optimized_script_content.count('\n') + 1
            optimized_chars = len(optimized_script_content)

            lines_diff = optimized_lines - original_lines
            chars_diff = optimized_chars - original_chars

            lines_percent = (lines_diff / original_lines) * 100 if original_lines > 0 else 0
            chars_percent = (chars_diff / original_chars) * 100 if original_chars > 0 else 0

            # Enhanced optimization summary with collapsible sections
            with st.expander("📊 Optimization Summary", expanded=True):
                st.markdown("### Before vs After Comparison")

                # Main metrics in a more visual layout
                col1, col2, col3, col4 = st.columns(4)

                with col1:
                    st.metric(
                        "Lines of Code",
                        f"{optimized_lines:,}",
                        delta=f"{lines_diff:+,} ({lines_percent:+.1f}%)",
                        delta_color="inverse" if lines_diff < 0 else "normal"
                    )

                with col2:
                    st.metric(
                        "Characters",
                        f"{optimized_chars:,}",
                        delta=f"{chars_diff:+,} ({chars_percent:+.1f}%)",
                        delta_color="inverse" if chars_diff < 0 else "normal"
                    )

                with col3:
                    if hasattr(state, 'optimization_start_time') and state.optimization_start_time:
                        optimization_duration = datetime.now() - state.optimization_start_time
                        duration_seconds = optimization_duration.total_seconds()
                        st.metric("Processing Time", f"{duration_seconds:.1f}s")
                    else:
                        st.metric("Processing Time", "N/A")

                with col4:
                    # Calculate efficiency improvement
                    efficiency_improvement = abs(chars_percent) if chars_diff < 0 else 0
                    st.metric(
                        "Efficiency Gain",
                        f"{efficiency_improvement:.1f}%",
                        help="Percentage reduction in code size"
                    )

                # Count imports, fixtures, and test functions in both scripts
                original_imports = len(re.findall(r'^import |^from ', original_script, re.MULTILINE))
                original_fixtures = len(re.findall(r'@pytest\.fixture', original_script, re.MULTILINE))
                original_tests = len(re.findall(r'def test_', original_script, re.MULTILINE))

                optimized_imports = len(re.findall(r'^import |^from ', optimized_script_content, re.MULTILINE))
                optimized_fixtures = len(re.findall(r'@pytest\.fixture', optimized_script_content, re.MULTILINE))
                optimized_tests = len(re.findall(r'def test_', optimized_script_content, re.MULTILINE))

                # Enhanced code structure comparison
                st.markdown("### Code Structure Analysis")
                col1, col2, col3 = st.columns(3)

                with col1:
                    imports_delta = optimized_imports - original_imports
                    st.metric(
                        "Import Statements",
                        f"{optimized_imports}",
                        delta=f"{imports_delta:+}" if imports_delta != 0 else None,
                        help=f"Original: {original_imports}"
                    )

                with col2:
                    fixtures_delta = optimized_fixtures - original_fixtures
                    st.metric(
                        "Pytest Fixtures",
                        f"{optimized_fixtures}",
                        delta=f"{fixtures_delta:+}" if fixtures_delta != 0 else None,
                        help=f"Original: {original_fixtures}"
                    )

                with col3:
                    tests_delta = optimized_tests - original_tests
                    st.metric(
                        "Test Functions",
                        f"{optimized_tests}",
                        delta=f"{tests_delta:+}" if tests_delta != 0 else None,
                        help=f"Original: {original_tests}"
                    )

                # Key improvements analysis
                st.markdown("### Key Improvements Made")
                improvements = []

                if imports_delta < 0:
                    improvements.append(f"🔧 **Import Optimization**: Reduced imports by {abs(imports_delta)} (removed duplicates/unused imports)")
                elif imports_delta > 0:
                    improvements.append(f"📦 **Import Enhancement**: Added {imports_delta} necessary imports for better functionality")

                if lines_diff < 0:
                    improvements.append(f"📉 **Code Consolidation**: Reduced code by {abs(lines_diff)} lines ({abs(lines_percent):.1f}% reduction)")

                if chars_diff < 0:
                    improvements.append(f"⚡ **Efficiency Boost**: Optimized code structure, reducing size by {abs(chars_diff)} characters")

                # Add some standard improvements that AI typically makes
                improvements.extend([
                    "🏗️ **Structure Enhancement**: Organized code into logical sections with proper imports, fixtures, and test functions",
                    "🧹 **Code Cleanup**: Removed redundant code and consolidated similar operations",
                    "📝 **Documentation**: Added clear comments and docstrings for better maintainability",
                    "✅ **Best Practices**: Applied PyTest best practices for robust test automation",
                    "🛡️ **Error Handling**: Improved exception handling and added appropriate safeguards"
                ])

                for improvement in improvements:
                    st.markdown(f"- {improvement}")

                logger.info(f"Stage 8: Enhanced optimization metrics - Original: {original_lines} lines, {original_chars} chars; " +
                           f"Optimized: {optimized_lines} lines, {optimized_chars} chars; " +
                           f"Diff: {lines_diff} lines ({lines_percent:.1f}%), {chars_diff} chars ({chars_percent:.1f}%)")

            # Display AI-powered validation results if available
            if hasattr(state, 'optimized_script_validation_done') and state.optimized_script_validation_done and hasattr(state, 'optimized_script_validation_results'):
                st.markdown("### 🔍 Code Quality Validation")

                validation_results = state.optimized_script_validation_results

                # Display validation results without scoring
                with st.expander("🤖 AI-Powered Validation Results", expanded=True):
                    # Display validation status with color coding
                    validation_status = validation_results.get('validation_status', 'unknown')
                    if validation_status == "excellent":
                        status_color = "🟢"
                        status_text = "Excellent"
                    elif validation_status == "good":
                        status_color = "🟡"
                        status_text = "Good"
                    elif validation_status == "needs_improvement":
                        status_color = "🟠"
                        status_text = "Needs Improvement"
                    elif validation_status == "failed":
                        status_color = "🔴"
                        status_text = "Failed"
                    else:
                        status_color = "⚪"
                        status_text = "Unknown"

                    col1, col2 = st.columns(2)
                    with col1:
                        st.metric("Validation Status", f"{status_color} {status_text}")
                    with col2:
                        confidence = validation_results.get('confidence_rating', 'medium').title()
                        st.metric("AI Confidence", confidence)

                    # Display syntax validation
                    syntax_valid = validation_results.get('syntax_valid', True)
                    if syntax_valid:
                        st.success("✅ Syntax validation passed")
                    else:
                        st.error("❌ Syntax issues detected")

                    # Display ready for execution status
                    ready_for_execution = validation_results.get('ready_for_execution', True)
                    if ready_for_execution:
                        st.success("✅ Script is ready for execution")
                    else:
                        st.warning("⚠️ Script may need review before execution")

                        # Display issues found
                        issues = validation_results.get('issues_found', [])
                        if issues:
                            st.subheader("Issues Found")
                            for issue in issues:
                                severity = issue.get('severity', 'medium')
                                category = issue.get('category', 'general')
                                description = issue.get('description', 'No description')

                                if severity == 'high':
                                    st.error(f"🔴 **{category.title()}**: {description}")
                                elif severity == 'medium':
                                    st.warning(f"🟡 **{category.title()}**: {description}")
                                else:
                                    st.info(f"🔵 **{category.title()}**: {description}")
                        else:
                            st.success("✅ No issues found in the optimized script")

                        # Display recommendations
                        recommendations = validation_results.get('recommendations', [])
                        if recommendations:
                            st.subheader("Recommendations")
                            for rec in recommendations:
                                st.markdown(f"💡 {rec}")

                        # Show validation error if any
                        if validation_results.get('validation_error'):
                            with st.expander("⚠️ Validation Error Details", expanded=False):
                                st.error(f"Validation Error: {validation_results['validation_error']}")

                    # Enhanced re-validation with comment input workflow
                    col1, col2, col3 = st.columns([1, 2, 1])
                    with col2:
                        if st.button("🔄 Re-validate Optimized Script", use_container_width=True, key="revalidate_optimized_script"):
                            logger.info("Stage 8: User clicked 'Re-validate Optimized Script' button")

                            with st.spinner("Re-validating optimized script..."):
                                try:
                                    # Prepare test data for validation (if available)
                                    test_data = {}
                                    if hasattr(state, 'manual_test_data') and state.manual_test_data:
                                        test_data = state.manual_test_data

                                    # Re-run validation
                                    validation_results = validate_generated_script(
                                        script_content=optimized_script_content,
                                        test_case=state.selected_test_case,
                                        step_table_entry=None,
                                        test_data=test_data,
                                        element_matches=None,
                                        api_key=state.google_api_key
                                    )

                                    # Store validation results in state
                                    state.optimized_script_validation_results = validation_results
                                    state.optimized_script_validation_done = True
                                    st.session_state['state'] = state

                                    logger.info(f"Stage 8: Re-validation completed with quality score: {validation_results.get('quality_score', 'N/A')}")
                                    st.success("✅ Re-validation completed!")
                                    st.rerun()

                                except Exception as e:
                                    error_msg = f"Error during re-validation: {str(e)}"
                                    st.error(error_msg)
                                    logger.error(f"Stage 8: {error_msg}")

                # Enhanced comment input section after re-validation
                if (hasattr(state, 'optimized_script_validation_done') and state.optimized_script_validation_done and
                    hasattr(state, 'optimized_script_validation_results') and state.optimized_script_validation_results):

                    validation_results = state.optimized_script_validation_results
                    validation_status = validation_results.get('validation_status', 'excellent')
                    has_issues = validation_status in ['needs_improvement', 'failed'] or not validation_results.get('ready_for_execution', True)

                    # Show comment input section if validation issues detected or user wants to provide feedback
                    if has_issues or st.checkbox("💬 Provide feedback for script improvement", key="show_comment_input"):
                        st.markdown("### 💬 Script Improvement Feedback")

                        with st.expander("✨ Provide Custom Instructions", expanded=True):
                            st.markdown("""
                            **Help improve the script optimization:** Share your feedback, specific requirements,
                            or issues you've noticed. This will be used to regenerate a better optimized script.
                            """)

                            # User comment input
                            user_comment = st.text_area(
                                "Your feedback/instructions:",
                                value=getattr(state, 'user_optimization_comment', ''),
                                height=100,
                                placeholder="e.g., 'Add more error handling', 'Improve variable naming', 'Fix the login logic', etc.",
                                key="user_optimization_comment_input",
                                help="Describe what you'd like to improve in the optimized script"
                            )

                            # Store user comment in state
                            if user_comment != getattr(state, 'user_optimization_comment', ''):
                                state.user_optimization_comment = user_comment
                                state.comment_enhancement_done = False  # Reset enhancement flag when comment changes
                                state.ai_enhanced_comment = ""
                                state.use_enhanced_comment = False
                                st.session_state['state'] = state

                            # AI Enhancement section
                            if user_comment.strip():
                                st.markdown("---")
                                st.markdown("**✨ AI Enhancement (Optional)**")

                                col1, col2 = st.columns([1, 1])

                                with col1:
                                    if st.button("✨ Enhance Comment with AI",
                                               use_container_width=True,
                                               key="enhance_comment_ai",
                                               help="Let AI enhance your feedback into detailed technical instructions"):
                                        logger.info("Stage 8: User clicked 'Enhance Comment with AI' button")

                                        with st.spinner("Enhancing your feedback with AI..."):
                                            try:
                                                enhanced_comment = enhance_user_comment_with_ai(
                                                    user_comment=user_comment,
                                                    validation_results=validation_results,
                                                    api_key=state.google_api_key,
                                                    context={
                                                        'test_case_id': state.selected_test_case.get('Test Case ID', 'unknown'),
                                                        'optimization_stage': 'stage8_revalidation'
                                                    }
                                                )

                                                # Store enhanced comment in state
                                                state.ai_enhanced_comment = enhanced_comment
                                                state.comment_enhancement_done = True
                                                st.session_state['state'] = state

                                                logger.info(f"Stage 8: AI comment enhancement completed, enhanced length: {len(enhanced_comment)}")
                                                st.success("✅ Comment enhanced successfully!")
                                                st.rerun()

                                            except Exception as e:
                                                error_msg = f"Error enhancing comment: {str(e)}"
                                                st.error(error_msg)
                                                logger.error(f"Stage 8: {error_msg}")

                                # Show enhanced comment if available
                                if (hasattr(state, 'comment_enhancement_done') and state.comment_enhancement_done and
                                    hasattr(state, 'ai_enhanced_comment') and state.ai_enhanced_comment):

                                    st.markdown("**📝 Enhanced Instructions:**")

                                    # Show both versions for comparison
                                    col1, col2 = st.columns(2)

                                    with col1:
                                        st.markdown("**Your Original Feedback:**")
                                        st.info(user_comment)

                                    with col2:
                                        st.markdown("**AI-Enhanced Instructions:**")
                                        st.success(state.ai_enhanced_comment)

                                    # User choice between original and enhanced
                                    st.markdown("**Choose which version to use for regeneration:**")

                                    comment_choice = st.radio(
                                        "Select instructions to use:",
                                        options=["Use Original Feedback", "Use AI-Enhanced Instructions"],
                                        index=1 if getattr(state, 'use_enhanced_comment', False) else 0,
                                        key="comment_choice_radio",
                                        horizontal=True
                                    )

                                    # Update state based on user choice
                                    use_enhanced = comment_choice == "Use AI-Enhanced Instructions"
                                    if use_enhanced != getattr(state, 'use_enhanced_comment', False):
                                        state.use_enhanced_comment = use_enhanced
                                        st.session_state['state'] = state

                # Check if user has provided custom instructions (regardless of validation status)
                has_custom_instructions = (
                    (hasattr(state, 'user_optimization_comment') and state.user_optimization_comment.strip()) or
                    (hasattr(state, 'ai_enhanced_comment') and state.ai_enhanced_comment.strip())
                )

                # Show regeneration option if validation indicates issues OR custom instructions are available
                show_regeneration = False
                validation_status = 'excellent'
                ready_for_execution = True

                if hasattr(state, 'optimized_script_validation_results') and state.optimized_script_validation_results:
                    validation_results = state.optimized_script_validation_results
                    validation_status = validation_results.get('validation_status', 'excellent')
                    ready_for_execution = validation_results.get('ready_for_execution', True)

                    # Show regeneration if validation indicates issues or custom instructions are available
                    show_regeneration = (
                        validation_status in ['needs_improvement', 'failed'] or
                        not ready_for_execution or
                        has_custom_instructions
                    )
                else:
                    # No validation results yet, but show regeneration if custom instructions are available
                    show_regeneration = has_custom_instructions

                if show_regeneration:
                    st.markdown("### 🔄 Regeneration Options")

                    # Determine the appropriate expander title and message based on context
                    if has_custom_instructions and validation_status not in ['needs_improvement', 'failed'] and ready_for_execution:
                        # User has custom instructions but validation is good
                        expander_title = "✨ Custom Optimization Options"
                        message_text = "You have provided custom instructions for script improvement. You can:"
                        message_type = "info"
                    else:
                        # Validation issues detected
                        expander_title = "⚠️ Quality Improvement Options"
                        message_text = "The validation indicates potential issues with the optimized script. You can:"
                        message_type = "warning"

                    with st.expander(expander_title, expanded=True):
                        if message_type == "warning":
                            st.warning(message_text)
                        else:
                            st.info(message_text)

                        col1, col2 = st.columns(2)

                        with col1:
                            if st.button("🔄 Regenerate Optimization", use_container_width=True, key="regenerate_optimization"):
                                logger.info("Stage 8: User clicked 'Regenerate Optimization' button")

                                # Check if user has provided custom instructions
                                custom_instructions = None
                                if hasattr(state, 'user_optimization_comment') and state.user_optimization_comment.strip():
                                    if hasattr(state, 'use_enhanced_comment') and state.use_enhanced_comment and hasattr(state, 'ai_enhanced_comment'):
                                        custom_instructions = state.ai_enhanced_comment
                                        logger.info("Stage 8: Using AI-enhanced comment for regeneration")
                                    else:
                                        custom_instructions = state.user_optimization_comment
                                        logger.info("Stage 8: Using original user comment for regeneration")

                                    logger.info(f"Stage 8: Custom instructions length: {len(custom_instructions)} characters")

                                # Store custom instructions for use during optimization
                                if custom_instructions:
                                    state.regeneration_custom_instructions = custom_instructions
                                    logger.info("Stage 8: Stored custom instructions for regeneration")
                                else:
                                    state.regeneration_custom_instructions = None
                                    logger.info("Stage 8: No custom instructions provided for regeneration")

                                # Reset optimization state to trigger regeneration
                                state.optimization_complete = False
                                state.optimization_in_progress = True
                                state.optimized_script_path = None
                                state.optimized_script_content = None
                                state.optimized_script_validation_results = None
                                state.optimized_script_validation_done = False
                                state.optimization_start_time = datetime.now()

                                # Set session state flag
                                st.session_state['optimization_in_progress'] = True
                                st.session_state['state'] = state

                                logger.info("Stage 8: Reset optimization state for regeneration with custom instructions")
                                if custom_instructions:
                                    st.success("🔄 Regenerating optimization with your custom instructions...")
                                else:
                                    st.success("🔄 Regenerating optimization...")
                                st.rerun()

                        with col2:
                            if st.button("📝 Proceed with Current Script", use_container_width=True, key="proceed_with_script"):
                                logger.info("Stage 8: User chose to proceed with current script despite validation issues")
                                st.info("✅ Proceeding with current optimized script. You can still test and download it below.")

            # Enhanced script viewing with better organization
            with st.expander("📄 View Optimized Script", expanded=True):
                st.markdown("**Optimized Test Script:**")
                st.code(optimized_script_content, language="python")
                st.info(f"📁 Script saved to: `{state.optimized_script_path}`")

            # Enhanced download functionality with better visual emphasis
            st.markdown("### 📥 Download Optimized Script")

            # Create a more descriptive filename
            test_case_id = state.selected_test_case.get('Test Case ID', 'unknown')
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            download_filename = f"test_{test_case_id}_optimized_{timestamp}.py"

            # Enhanced download button with better styling
            col1, col2, col3 = st.columns([1, 2, 1])
            with col2:
                download_clicked = st.download_button(
                    label="📥 Download Optimized Script",
                    data=optimized_script_content,
                    file_name=download_filename,
                    mime="text/plain",
                    key="download_optimized_script",
                    use_container_width=True,
                    help=f"Download the optimized test script as {download_filename}"
                )

                if download_clicked:
                    logger.info(f"Stage 8: User downloaded optimized script as {download_filename}")
                    st.success(f"✅ Script downloaded as `{download_filename}`")

            # Side-by-side comparison option
            with st.expander("🔍 Side-by-Side Comparison", expanded=False):
                st.markdown("**Compare Original vs Optimized Scripts:**")

                col1, col2 = st.columns(2)

                with col1:
                    st.markdown("**Original Script (Before Optimization):**")
                    st.code(original_script, language="python", line_numbers=True)

                with col2:
                    st.markdown("**Optimized Script (After Optimization):**")
                    st.code(optimized_script_content, language="python", line_numbers=True)

        except Exception as e:
            error_msg = f"Error displaying optimization results: {str(e)}"
            st.error(error_msg)
            logger.error(f"Stage 8: {error_msg}", exc_info=True)

        # Test Optimized Script Feature
        st.markdown("---")  # Visual separator
        st.markdown("### 🧪 Test Optimized Script")

        # Check if we have test results already
        has_test_results = hasattr(state, 'optimized_script_test_results') and state.optimized_script_test_results

        if has_test_results:
            # Display existing test results
            test_results = state.optimized_script_test_results

            if test_results.get('success', False):
                st.success("✅ Optimized script test passed!")
                st.markdown("**Test Status:** The optimized script executed successfully and maintains the same functionality as the original.")
            else:
                st.error("❌ Optimized script test failed!")
                st.markdown("**Test Status:** The optimized script encountered issues during execution. Review the details below.")

            # Show test results in an expander
            with st.expander("📋 Test Execution Results", expanded=True):
                col1, col2 = st.columns(2)

                with col1:
                    st.markdown("**Test Summary:**")
                    st.write(f"- **Return Code:** {test_results.get('returncode', 'N/A')}")
                    st.write(f"- **Success:** {'✅ Yes' if test_results.get('success', False) else '❌ No'}")
                    st.write(f"- **Script Path:** `{os.path.basename(test_results.get('script_path', 'N/A'))}`")

                    if test_results.get('screenshots'):
                        st.write(f"- **Screenshots:** {len(test_results['screenshots'])} captured")

                with col2:
                    st.markdown("**Test Output:**")
                    if test_results.get('stdout'):
                        st.code(test_results['stdout'], language="text")
                    else:
                        st.info("No standard output captured")

                if test_results.get('stderr'):
                    st.markdown("**Errors/Warnings:**")
                    st.code(test_results['stderr'], language="text")

                # Show screenshots if any were captured
                if test_results.get('screenshots'):
                    st.markdown("**Screenshots Captured:**")
                    for i, screenshot_path in enumerate(test_results['screenshots'][:3]):  # Show up to 3 screenshots
                        if os.path.exists(screenshot_path):
                            st.image(screenshot_path, caption=f"Screenshot {i+1}: {os.path.basename(screenshot_path)}")

            # Option to re-test
            col1, col2, col3 = st.columns([1, 2, 1])
            with col2:
                if st.button("🔄 Re-test Optimized Script", use_container_width=True, key="retest_optimized_script"):
                    logger.info("Stage 8: User clicked 'Re-test Optimized Script' button")
                    # Clear previous test results
                    state.optimized_script_test_results = None
                    st.session_state['state'] = state
                    st.rerun()

        else:
            # Show test option
            st.markdown("""
            **Verify Functionality:** Test the optimized script to ensure it maintains the same functionality as the original combined script.
            This will execute the optimized script using the same testing infrastructure as Stage 7.
            """)

            col1, col2, col3 = st.columns([1, 2, 1])
            with col2:
                if st.button("🧪 Test Optimized Script", use_container_width=True, key="test_optimized_script", type="secondary"):
                    logger.info("Stage 8: User clicked 'Test Optimized Script' button")

                    with st.spinner("Testing optimized script... This may take a moment."):
                        try:
                            # Execute the optimized script using our reusable function
                            test_results = execute_test_script(
                                script_path=state.optimized_script_path,
                                test_context="optimized script"
                            )

                            # Store the test results in state
                            state.optimized_script_test_results = test_results
                            st.session_state['state'] = state

                            logger.info(f"Stage 8: Optimized script test completed with success: {test_results.get('success', False)}")

                            # Show immediate feedback
                            if test_results.get('success', False):
                                st.success("✅ Optimized script test passed! The script maintains functionality.")
                            else:
                                st.error("❌ Optimized script test failed. Check the results below.")

                            # Rerun to show the detailed results
                            st.rerun()

                        except Exception as e:
                            error_msg = f"Error testing optimized script: {str(e)}"
                            st.error(error_msg)
                            logger.error(f"Stage 8: {error_msg}", exc_info=True)

        # Enhanced workflow transition section
        st.markdown("---")  # Visual separator
        st.markdown("### 🎯 Next Steps")

        # Add a visually appealing completion message
        st.markdown("""
        <div style="text-align: center; margin: 20px 0; padding: 15px; background: linear-gradient(135deg, #e8f5e9 0%, #f1f8e9 100%); border-radius: 12px; border: 2px solid #4CAF50; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <div style="font-size: 24px; margin-bottom: 8px;">🎉</div>
            <p style="font-size: 18px; color: #2E7D32; margin: 0; font-weight: 600;">Script Optimization Complete!</p>
            <p style="font-size: 14px; color: #388E3C; margin: 8px 0 0 0;">Your test script has been successfully optimized and is ready for use.</p>
        </div>
        """, unsafe_allow_html=True)

        # Workflow options
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**✅ What you can do now:**")

            # Get test status
            test_status = ""
            if hasattr(state, 'optimized_script_test_results') and state.optimized_script_test_results:
                if state.optimized_script_test_results.get('success', False):
                    test_status = " ✅ (Tested & Verified)"
                else:
                    test_status = " ⚠️ (Test Failed - Review Required)"

            # Get validation status
            validation_status = ""
            if hasattr(state, 'optimized_script_validation_results') and state.optimized_script_validation_results:
                validation_result_status = state.optimized_script_validation_results.get('validation_status', 'unknown')
                ready_for_execution = state.optimized_script_validation_results.get('ready_for_execution', True)

                if validation_result_status == 'excellent' and ready_for_execution:
                    validation_status = " 🟢 (Excellent)"
                elif validation_result_status == 'good' and ready_for_execution:
                    validation_status = " 🟡 (Good)"
                elif validation_result_status == 'needs_improvement':
                    validation_status = " 🟠 (Needs Improvement)"
                elif validation_result_status == 'failed':
                    validation_status = " 🔴 (Failed)"
                else:
                    validation_status = " ⚪ (Unknown)"

            st.markdown(f"""
            - Download your optimized script{test_status}{validation_status}
            - Test the optimized script functionality
            - Review the AI validation results
            - Review the optimization improvements
            - Compare original vs optimized versions
            - Use the script in your testing environment
            """)

        with col2:
            st.markdown("**🔄 Continue workflow:**")
            st.markdown("""
            - Browse and compare all generated scripts
            - Return to Phase 3 to select another test case
            - Generate scripts for additional test scenarios
            - Build a comprehensive test suite
            """)

        # Enhanced navigation section with Script Browser option
        st.markdown("### 🧭 Next Steps")

        col1, col2 = st.columns(2)

        with col1:
            if st.button("📜 Browse Scripts (Phase 9)", use_container_width=True, type="secondary"):
                logger.info("Phase 8: User clicked 'Browse Scripts' button")

                # Add current optimized script to history for browsing
                if hasattr(state, 'optimized_script_content') and state.optimized_script_content:
                    state.add_script_to_history(
                        script_content=state.optimized_script_content,
                        script_type='optimized',
                        step_no=None,
                        file_path=getattr(state, 'optimized_script_path', None),
                        metadata={
                            'optimization_complete': True,
                            'test_case_id': state.selected_test_case.get('Test Case ID', 'unknown'),
                            'optimization_timestamp': datetime.now().isoformat()
                        }
                    )

                # Advance to Stage 9
                state.advance_to(StateStage.STAGE9_BROWSE, "User chose to browse scripts from Stage 8")
                st.session_state['state'] = state
                st.rerun()

        with col2:
            if st.button("🔄 New Test Case (Phase 3)", use_container_width=True, type="primary"):
                logger.info("Phase 8: User clicked 'Return to Test Case Selection' button")

                # Reset test case state with confirmation (this will also handle stage transition to Stage 3)
                state.reset_test_case_state(confirm=True, reason="Script optimization complete, returning to Phase 3")
                logger.info("Phase 8: Reset test case state to prepare for new test case")

                # Force state update in session state
                st.session_state['state'] = state
                st.session_state['stage_progression_message'] = "✅ Script optimization complete. Returning to Phase 3 to select a new test case."

                # Use one-shot flag to indicate we're transitioning from Phase 8 to Phase 3
                from utils.flag_helpers import one_shot_flag
                with one_shot_flag('transitioning_from_stage8'):
                    logger.info("Setting one-shot transitioning_from_stage8 flag for Stage 8 -> Stage 3 transition")
                    st.rerun()

        return

    # Check if optimization is in progress
    logger.info("Stage 8: Checking optimization in progress status")
    logger.info(f"Stage 8: optimization_in_progress = {getattr(state, 'optimization_in_progress', 'NOT_SET')}")
    logger.info(f"Stage 8: session_state optimization_in_progress = {st.session_state.get('optimization_in_progress', 'NOT_SET')}")

    # If optimization is in progress, show a spinner
    if hasattr(state, 'optimization_in_progress') and state.optimization_in_progress:
        logger.info("=" * 80)
        logger.info("STAGE 8: OPTIMIZATION IN PROGRESS - PROCESSING")
        logger.info("=" * 80)
        logger.info("Stage 8: Detected optimization_in_progress=True, starting optimization process")

        with st.spinner("Optimizing script... This may take a minute or two."):
            try:
                # Load the final merged script from Stage 7
                script_to_optimize = None
                script_source = None

                # Log script state before optimization loading
                _log_script_state_debug(state, "BEFORE_OPTIMIZATION_LOADING", _debug_log_path)

                # Try to load from file first
                if hasattr(state, 'combined_script_path') and state.combined_script_path and os.path.exists(state.combined_script_path):
                    try:
                        with open(state.combined_script_path, 'r') as f:
                            script_to_optimize = f.read()
                        script_source = "final_script_file"
                        logger.info(f"Stage 8: Successfully loaded final script from file: {state.combined_script_path}")
                        logger.info(f"Stage 8: Final script length: {len(script_to_optimize)} characters")
                        # Log successful file loading for optimization
                        _log_script_state_debug(state, "OPTIMIZATION_LOADED_FROM_FILE", _debug_log_path,
                                               error_info=f"Source: {script_source}, Length: {len(script_to_optimize)}")
                    except Exception as e:
                        logger.error(f"Stage 8: Failed to read final script file: {str(e)}")
                        # Log file loading error
                        _log_script_state_debug(state, "OPTIMIZATION_FILE_LOAD_ERROR", _debug_log_path,
                                               error_info=f"Failed to read file: {str(e)}")

                # Fall back to content in state
                if not script_to_optimize and hasattr(state, 'combined_script_content') and state.combined_script_content:
                    script_to_optimize = state.combined_script_content
                    script_source = "final_script_content"
                    logger.info(f"Stage 8: Using final script content from state ({len(script_to_optimize)} characters)")
                    # Log successful content loading for optimization
                    _log_script_state_debug(state, "OPTIMIZATION_LOADED_FROM_CONTENT", _debug_log_path,
                                           error_info=f"Source: {script_source}, Length: {len(script_to_optimize)}")

                # Validate that we have a script to optimize
                if not script_to_optimize:
                    error_msg = "No final script available for optimization. Please complete Stage 7 first."
                    logger.error(f"Stage 8: {error_msg}")
                    st.error(error_msg)
                    # Log optimization failure due to no script
                    _log_script_state_debug(state, "OPTIMIZATION_NO_SCRIPT_ERROR", _debug_log_path,
                                           error_info="No script available for optimization")
                    state.optimization_in_progress = False
                    if 'optimization_in_progress' in st.session_state:
                        del st.session_state['optimization_in_progress']
                    st.session_state['state'] = state
                    st.rerun()
                    return

                # Log script information
                logger.info(f"Stage 8: Using script from source: {script_source}")
                logger.info(f"Stage 8: Script length: {len(script_to_optimize)} characters")
                if script_to_optimize:
                    logger.info(f"Stage 8: Script preview (first 200 chars): {script_to_optimize[:200]}...")

                # Use the selected script for optimization (rename variable for clarity)
                combined_script = script_to_optimize

                # Initialize optimization start time if not already set
                if not hasattr(state, 'optimization_start_time') or not state.optimization_start_time:
                    state.optimization_start_time = datetime.now()
                    logger.info(f"Stage 8: Initializing optimization start time to {state.optimization_start_time}")
                else:
                    logger.info(f"Stage 8: Using existing optimization start time: {state.optimization_start_time}")

                # Log optimization environment
                logger.info(f"Stage 8: Google AI API key available: {bool(state.google_api_key)}")
                logger.info(f"Stage 8: Selected test case: {state.selected_test_case.get('Test Case ID', 'Unknown')}")
                logger.info(f"Stage 8: Combined script path: {getattr(state, 'combined_script_path', 'NOT_SET')}")

                # Check if the script is too large for a single optimization
                if len(combined_script) > 30000:  # 30k character limit for safety
                    st.info("Script is large, using chunked optimization approach...")
                    logger.info("Stage 8: Script exceeds 30k characters, using chunked optimization approach")

                    # Split the script into logical sections
                    # This is a simple approach - in a real implementation, you'd want more sophisticated parsing

                    # Extract imports section (all lines starting with import or from)
                    imports_pattern = r'((?:^import .*$|^from .*$)(?:\n|$))+'
                    imports_match = re.search(imports_pattern, combined_script, re.MULTILINE)
                    imports_section = imports_match.group(0) if imports_match else ""
                    logger.info(f"Stage 8: Extracted imports section ({len(imports_section)} characters)")

                    # Extract fixtures section (all @pytest.fixture blocks)
                    fixtures_pattern = r'(@pytest\.fixture.*?def .*?\):(?:\n    .*?)*?)(?=\n\n)'
                    fixtures_matches = re.finditer(fixtures_pattern, combined_script, re.DOTALL)
                    fixtures_section = "\n\n".join([m.group(0) for m in fixtures_matches]) if fixtures_matches else ""
                    logger.info(f"Stage 8: Extracted fixtures section ({len(fixtures_section)} characters)")

                    # Extract test functions (all def test_* blocks)
                    test_pattern = r'(def test_.*?\):(?:\n    .*?)*?)(?=\n\n|$)'
                    test_matches = re.finditer(test_pattern, combined_script, re.DOTALL)
                    test_functions_section = "\n\n".join([m.group(0) for m in test_matches]) if test_matches else ""
                    logger.info(f"Stage 8: Extracted test functions section ({len(test_functions_section)} characters)")

                    # Check if we successfully extracted all sections
                    if not imports_section and not fixtures_section and not test_functions_section:
                        logger.warning("Stage 8: Failed to extract any sections from the script, falling back to full script optimization")
                        st.warning("Could not split script into sections, attempting full optimization...")
                        # Fall back to optimizing the entire script
                        custom_instructions = getattr(state, 'regeneration_custom_instructions', None)
                        optimized_script = optimize_script_with_ai(
                            combined_script,
                            api_key=state.google_api_key,
                            custom_instructions=custom_instructions
                        )
                        if custom_instructions:
                            logger.info(f"Stage 8: Applied custom instructions to full script optimization: {len(custom_instructions)} chars")
                    else:
                        # Optimize each section separately
                        logger.info("Stage 8: Optimizing imports section")
                        optimized_imports = optimize_script_with_ai(
                            imports_section,
                            api_key=state.google_api_key,
                            chunk_mode=True,
                            chunk_info={'chunk_number': 1, 'total_chunks': 3, 'chunk_type': 'imports'}
                        )
                        logger.info(f"Stage 8: Imports section optimization complete ({len(optimized_imports)} characters)")

                        logger.info("Stage 8: Optimizing fixtures section")
                        optimized_fixtures = optimize_script_with_ai(
                            fixtures_section,
                            api_key=state.google_api_key,
                            chunk_mode=True,
                            chunk_info={'chunk_number': 2, 'total_chunks': 3, 'chunk_type': 'fixtures'}
                        )
                        logger.info(f"Stage 8: Fixtures section optimization complete ({len(optimized_fixtures)} characters)")

                        logger.info("Stage 8: Optimizing test functions section")
                        optimized_tests = optimize_script_with_ai(
                            test_functions_section,
                            api_key=state.google_api_key,
                            chunk_mode=True,
                            chunk_info={'chunk_number': 3, 'total_chunks': 3, 'chunk_type': 'test_functions'}
                        )
                        logger.info(f"Stage 8: Test functions section optimization complete ({len(optimized_tests)} characters)")

                        # Combine the optimized sections
                        optimized_script = f"{optimized_imports}\n\n{optimized_fixtures}\n\n{optimized_tests}"
                        logger.info(f"Stage 8: Combined optimized sections into final script ({len(optimized_script)} characters)")
                else:
                    # Optimize the entire script at once
                    logger.info("Stage 8: Optimizing entire script in one call")
                    custom_instructions = getattr(state, 'regeneration_custom_instructions', None)
                    optimized_script = optimize_script_with_ai(
                        combined_script,
                        api_key=state.google_api_key,
                        custom_instructions=custom_instructions
                    )
                    if custom_instructions:
                        logger.info(f"Stage 8: Applied custom instructions to script optimization: {len(custom_instructions)} chars")
                    logger.info(f"Stage 8: Script optimization complete ({len(optimized_script)} characters)")

                # Save the optimized script to a file
                script_dir = "generated_tests"
                os.makedirs(script_dir, exist_ok=True)
                logger.info(f"Stage 8: Ensured output directory exists: {script_dir}")

                # Get the test case ID
                test_case_id = state.selected_test_case.get('Test Case ID', 'unknown')

                # Create a file path for the optimized script
                timestamp = int(time.time())
                optimized_script_file = os.path.join(
                    script_dir,
                    f"test_{test_case_id}_optimized_{timestamp}.py"
                )
                logger.info(f"Stage 8: Created output file path: {optimized_script_file}")

                # Save the optimized script to a file
                try:
                    with open(optimized_script_file, "w") as f:
                        f.write(optimized_script)
                    logger.info(f"Stage 8: Successfully saved optimized script to {optimized_script_file}")
                except Exception as e:
                    error_msg = f"Error saving optimized script: {str(e)}"
                    logger.error(f"Stage 8: {error_msg}")
                    st.error(error_msg)
                    state.optimization_in_progress = False

                    # Clear the session state flag as well
                    if 'optimization_in_progress' in st.session_state:
                        del st.session_state['optimization_in_progress']
                        logger.error("Stage 8: Cleared optimization_in_progress flag from session state due to file save error")

                    st.session_state['state'] = state
                    st.rerun()
                    return

                # Calculate optimization duration
                end_time = datetime.now()
                if hasattr(state, 'optimization_start_time') and state.optimization_start_time:
                    optimization_duration = end_time - state.optimization_start_time
                    duration_seconds = optimization_duration.total_seconds()
                    logger.info(f"Stage 8: Optimization completed in {duration_seconds:.2f} seconds")
                    logger.info(f"Stage 8: Start time: {state.optimization_start_time}")
                    logger.info(f"Stage 8: End time: {end_time}")
                else:
                    logger.warning("Stage 8: Could not calculate optimization duration - start time not available")

                # Update the state with results
                logger.info("Stage 8: Updating state with optimization results")
                state.optimized_script_path = optimized_script_file
                state.optimized_script_content = optimized_script
                state.optimization_complete = True
                state.optimization_in_progress = False

                # Log successful optimization completion
                _log_script_state_debug(state, "OPTIMIZATION_SUCCESS", _debug_log_path,
                                       error_info=f"Optimization completed successfully. Output file: {optimized_script_file}, Length: {len(optimized_script)}")

                # Clear the session state flag as well
                if 'optimization_in_progress' in st.session_state:
                    del st.session_state['optimization_in_progress']
                    logger.info("Stage 8: Cleared optimization_in_progress flag from session state")

                logger.info(f"Stage 8: Set optimized_script_path = {optimized_script_file}")
                logger.info(f"Stage 8: Set optimized_script_content length = {len(optimized_script)} characters")
                logger.info("Stage 8: Set optimization_complete = True")
                logger.info("Stage 8: Set optimization_in_progress = False")

                # Perform AI-powered validation of the optimized script
                logger.info("Stage 8: Starting automated validation of optimized script")
                try:
                    # Prepare test data for validation (if available)
                    test_data = {}
                    if hasattr(state, 'manual_test_data') and state.manual_test_data:
                        test_data = state.manual_test_data
                        logger.info(f"Stage 8: Using manual test data for validation ({len(test_data)} entries)")

                    # Perform validation using the same system as Stage 6
                    validation_results = validate_generated_script(
                        script_content=optimized_script,
                        test_case=state.selected_test_case,
                        step_table_entry=None,  # Not applicable for optimized scripts
                        test_data=test_data,
                        element_matches=None,  # Not applicable for optimized scripts
                        api_key=state.google_api_key
                    )

                    # Store validation results in state
                    state.optimized_script_validation_results = validation_results
                    state.optimized_script_validation_done = True

                    logger.info(f"Stage 8: Optimized script validation completed")
                    logger.info(f"Stage 8: Quality score: {validation_results.get('quality_score', 'N/A')}")
                    logger.info(f"Stage 8: Syntax valid: {validation_results.get('syntax_valid', 'N/A')}")
                    logger.info(f"Stage 8: Ready for execution: {validation_results.get('ready_for_execution', 'N/A')}")
                    logger.info(f"Stage 8: Issues found: {len(validation_results.get('issues_found', []))}")

                except Exception as validation_error:
                    logger.error(f"Stage 8: Error during optimized script validation: {validation_error}")
                    # Store error information but don't fail the optimization
                    state.optimized_script_validation_results = {
                        "quality_score": 0,
                        "syntax_valid": False,
                        "issues_found": [{"category": "validation", "severity": "high", "description": f"Validation error: {str(validation_error)}"}],
                        "recommendations": ["Review script manually", "Retry validation"],
                        "confidence_rating": "low",
                        "ready_for_execution": False,
                        "validation_error": str(validation_error)
                    }
                    state.optimized_script_validation_done = True
                    logger.error("Stage 8: Stored validation error results in state")

                # Automatically add optimized script to history for Script Browser
                logger.info("Stage 8: Adding optimized script to history for Script Browser")
                optimization_metadata = {
                    'optimization_complete': True,
                    'test_case_id': test_case_id,
                    'optimization_timestamp': datetime.now().isoformat(),
                    'optimization_duration': duration_seconds if 'duration_seconds' in locals() else None,
                    'script_source': script_source,
                    'validation_results': getattr(state, 'optimized_script_validation_results', {}),
                    'original_script_path': getattr(state, 'combined_script_path', None),
                    'optimization_status': 'optimized'
                }

                state.add_script_to_history(
                    script_content=optimized_script,
                    script_type='optimized',
                    step_no=None,
                    file_path=optimized_script_file,
                    metadata=optimization_metadata
                )
                logger.info("Stage 8: Successfully added optimized script to history")

                # Force state update in session state
                st.session_state['state'] = state
                logger.info("Stage 8: Updated session state with optimization results")
                logger.info("=" * 80)
                logger.info("STAGE 8: OPTIMIZATION COMPLETED SUCCESSFULLY")
                logger.info("=" * 80)

                # Set success message for user feedback
                st.session_state['stage_progression_message'] = "✅ Script optimization completed successfully. Choose your next action below."

                logger.info("Stage 8: Optimization completed - staying in Stage 8 for manual navigation")
                logger.info("Stage 8: Calling st.rerun() to display optimization results and navigation options")
                st.rerun()

            except Exception as e:
                error_msg = f"Error optimizing script: {str(e)}"
                st.error(error_msg)
                logger.error("=" * 80)
                logger.error("STAGE 8: OPTIMIZATION FAILED WITH ERROR")
                logger.error("=" * 80)
                logger.error(f"Stage 8: CRITICAL ERROR during optimization - {error_msg}", exc_info=True)
                logger.error(f"Stage 8: Exception type: {type(e)}")
                logger.error(f"Stage 8: Exception args: {e.args}")

                # Log optimization failure with script state
                _log_script_state_debug(state, "OPTIMIZATION_FAILED", _debug_log_path,
                                       error_info=f"Optimization failed with error: {error_msg}")

                # Reset optimization state on error
                state.optimization_in_progress = False

                # Clear the session state flag as well
                if 'optimization_in_progress' in st.session_state:
                    del st.session_state['optimization_in_progress']
                    logger.error("Stage 8: Cleared optimization_in_progress flag from session state due to optimization error")

                st.session_state['state'] = state

                # Log current state for debugging
                logger.error(f"Stage 8: Current state at error - optimization_in_progress: {getattr(state, 'optimization_in_progress', 'NOT_SET')}")
                logger.error(f"Stage 8: Current state at error - optimization_start_time: {getattr(state, 'optimization_start_time', 'NOT_SET')}")
                # Safe length check for combined_script_content (handles None values)
                combined_content = getattr(state, 'combined_script_content', '')
                combined_content_length = len(combined_content) if combined_content is not None else 0
                logger.error(f"Stage 8: Current state at error - combined_script_content length: {combined_content_length}")

                # Show detailed error information in an expander for debugging
                with st.expander("Error Details (for debugging)", expanded=False):
                    import traceback
                    error_traceback = traceback.format_exc()
                    st.code(error_traceback, language="text")
                    logger.error(f"Stage 8: Detailed error traceback: {error_traceback}")

                # Provide user-friendly error guidance
                st.info("""
                **Troubleshooting Steps:**
                1. Check your Google AI API key is valid and has sufficient quota
                2. Ensure the final script from Stage 7 is not corrupted
                3. Try refreshing the page and starting over
                4. If the issue persists, check the logs for more details
                """)

                # Reset the optimization state
                logger.error("Stage 8: Resetting optimization state due to error")
                state.optimization_in_progress = False
                state.optimization_start_time = None
                state.optimization_complete = False
                logger.error("Stage 8: Set optimization_in_progress = False")
                logger.error("Stage 8: Set optimization_start_time = None")
                logger.error("Stage 8: Set optimization_complete = False")

                # Force state update in session state
                st.session_state['state'] = state
                logger.error("Stage 8: Updated session state after error")
                logger.error("Stage 8: Calling st.rerun() after error")
                st.rerun()

    # Enhanced prerequisites check before showing optimization options
    logger.info("Stage 8: Starting prerequisites validation check")
    prerequisites_met, missing_items = _check_optimization_prerequisites(state)

    if not prerequisites_met:
        logger.warning("=" * 60)
        logger.warning("STAGE 8: PREREQUISITES NOT MET")
        logger.warning("=" * 60)
        logger.warning(f"Stage 8: Prerequisites validation failed - missing: {missing_items}")
        logger.warning(f"Stage 8: Total missing items count: {len(missing_items)}")
        for item in missing_items:
            logger.warning(f"Stage 8: Missing prerequisite: {item}")
        logger.warning("Stage 8: Displaying prerequisites guidance to user")
        _show_prerequisites_guidance(state, missing_items)
        logger.info("Stage 8: Exiting function due to missing prerequisites")
        return
    else:
        logger.info("=" * 60)
        logger.info("STAGE 8: ALL PREREQUISITES MET")
        logger.info("=" * 60)
        logger.info("Stage 8: Prerequisites validation passed - proceeding with optimization interface")

    # If optimization is not in progress and not complete, show the optimization options
    st.markdown("#### Optimization Options")

    # Display information about the optimization process
    with st.expander("About Script Optimization", expanded=False):
        st.info("""
        This stage will optimize the final merged script from Stage 7 using Google AI to create a well-structured,
        cohesive PyTest module following best practices. The optimization process will:

        1. Refactor the script into a well-structured PyTest module
        2. Ensure proper test setup and teardown with appropriate fixtures
        3. Remove redundancies and optimize the code
        4. Maintain all functionality from the merged test steps
        5. Follow best practices for PyTest automation
        6. Add clear comments explaining the optimization process
        7. Preserve all imports, assertions, and test logic
        8. **Validate the optimized script** using AI-powered quality analysis
        """)

        # Add more detailed information about what happens during optimization
        st.markdown("""
        #### What happens during optimization?

        The optimization process analyzes your final merged script from Stage 7 and makes improvements in several areas:

        - **Code Structure**: Organizes imports, fixtures, and test functions in a logical way
        - **Redundancy Removal**: Eliminates duplicate code and consolidates similar operations
        - **Error Handling**: Improves exception handling and adds appropriate try/except blocks
        - **Documentation**: Adds clear comments and docstrings to explain the test flow
        - **Best Practices**: Applies PyTest best practices for maintainable test automation

        #### AI-Powered Quality Validation

        After optimization, the system automatically validates the optimized script using the same
        AI-powered validation system from Stage 6:

        - **Syntax Analysis**: Verifies Python syntax and import correctness
        - **WebDriver Usage**: Checks proper browser fixture usage and locator strategies
        - **Test Structure**: Validates pytest conventions and test organization
        - **Quality Assessment**: Provides detailed validation status with feedback
        - **Issue Detection**: Identifies potential problems and provides recommendations
        - **Regeneration Options**: Allows re-optimization if quality issues are detected

        The original functionality is preserved while making the code more efficient and easier to maintain.
        """)

    # Display script statistics for the final script from Stage 7
    script_for_stats = None

    # Load final script for statistics
    if hasattr(state, 'combined_script_path') and state.combined_script_path and os.path.exists(state.combined_script_path):
        try:
            with open(state.combined_script_path, 'r') as f:
                script_for_stats = f.read()
        except Exception as e:
            logger.warning(f"Stage 8: Failed to read final script for statistics: {str(e)}")
    elif hasattr(state, 'combined_script_content') and state.combined_script_content:
        script_for_stats = state.combined_script_content

    if script_for_stats:
        line_count = script_for_stats.count('\n') + 1
        char_count = len(script_for_stats)

        # Count imports, fixtures, and test functions
        import_count = len(re.findall(r'^import |^from ', script_for_stats, re.MULTILINE))
        fixture_count = len(re.findall(r'@pytest\.fixture', script_for_stats, re.MULTILINE))
        test_count = len(re.findall(r'def test_', script_for_stats, re.MULTILINE))

        st.markdown("#### Final Script Statistics")
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Lines", f"{line_count:,}")
        with col2:
            st.metric("Imports", f"{import_count}")
        with col3:
            st.metric("Fixtures", f"{fixture_count}")
        with col4:
            st.metric("Test Functions", f"{test_count}")

        logger.info(f"Stage 8: Final script statistics - Lines: {line_count}, Chars: {char_count}, Imports: {import_count}, Fixtures: {fixture_count}, Tests: {test_count}")
    else:
        st.markdown("#### Final Script Statistics")
        st.info("📊 No final script available for statistics. Please complete Stage 7 first.")

    # Log that we're about to render the optimization button
    logger.info("=" * 80)
    logger.info("STAGE 8: RENDERING OPTIMIZATION BUTTON")
    logger.info("=" * 80)
    logger.info("Stage 8: About to render the 'Start Script Optimization' button")
    logger.info("Stage 8: All conditions passed - optimization not complete and not in progress")
    logger.info(f"Stage 8: Current state summary:")
    logger.info(f"  - optimization_complete: {getattr(state, 'optimization_complete', 'NOT_SET')}")
    logger.info(f"  - optimization_in_progress: {getattr(state, 'optimization_in_progress', 'NOT_SET')}")
    logger.info(f"  - combined_script_content length: {len(getattr(state, 'combined_script_content', ''))}")
    logger.info(f"  - google_api_key present: {bool(getattr(state, 'google_api_key', None))}")

    # Add a button to start the optimization with clear visual emphasis
    st.markdown("""
    <div style="text-align: center; margin: 20px 0; padding: 10px; background-color: #e8f5e9; border-radius: 10px; border: 1px solid #4CAF50;">
        <p style="font-size: 16px; color: #4CAF50; margin: 0;">Ready to optimize your test script?</p>
        <p style="font-size: 14px; color: #4CAF50; margin: 5px 0 0 0;">Click the button below to start the optimization process</p>
    </div>
    """, unsafe_allow_html=True)

    if st.button("Start Script Optimization", use_container_width=True, key="start_optimization_button"):
        logger.info("=" * 80)
        logger.info("STAGE 8: USER CLICKED START OPTIMIZATION BUTTON")
        logger.info("=" * 80)
        logger.info("Phase 8: User clicked 'Start Script Optimization' button")
        logger.info(f"Phase 8: Button click timestamp: {datetime.now()}")

        # Log script state at button click
        _log_script_state_debug(state, "BUTTON_CLICK_START_OPTIMIZATION", _debug_log_path)

        # Enhanced debugging for button click
        logger.info("Phase 8: ENHANCED DEBUGGING - Button click detected")
        logger.info(f"Phase 8: Current session state keys: {list(st.session_state.keys())}")
        logger.info(f"Phase 8: Current state object type: {type(state)}")
        logger.info(f"Phase 8: State has google_api_key: {hasattr(state, 'google_api_key')}")
        logger.info(f"Phase 8: State has combined_script_content: {hasattr(state, 'combined_script_content')}")

        # Show immediate feedback to user with enhanced visibility
        st.balloons()  # Visual feedback that button was clicked

        # Create a prominent feedback message
        feedback_container = st.container()
        with feedback_container:
            st.markdown("""
            <div style="text-align: center; margin: 20px 0; padding: 15px; background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); border-radius: 12px; border: 2px solid #2196F3; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <div style="font-size: 24px; margin-bottom: 8px;">🔄</div>
                <p style="font-size: 18px; color: #1976D2; margin: 0; font-weight: 600;">Processing Optimization Request</p>
                <p style="font-size: 14px; color: #1976D2; margin: 8px 0 0 0;">Button click detected - validating prerequisites...</p>
            </div>
            """, unsafe_allow_html=True)

        # Add comprehensive debugging
        logger.info("=" * 50)
        logger.info("COMPREHENSIVE BUTTON CLICK DEBUGGING")
        logger.info("=" * 50)

        # Debug state object
        logger.info(f"State object ID: {id(state)}")
        logger.info(f"State object attributes: {[attr for attr in dir(state) if not attr.startswith('_')]}")

        # Debug Google API key
        if hasattr(state, 'google_api_key'):
            api_key_status = "SET" if state.google_api_key else "EMPTY"
            logger.info(f"Google API key status: {api_key_status}")
        else:
            logger.info("Google API key attribute: NOT_FOUND")

        # Debug combined script content
        if hasattr(state, 'combined_script_content'):
            content_length = len(state.combined_script_content) if state.combined_script_content else 0
            logger.info(f"Combined script content length: {content_length}")
        else:
            logger.info("Combined script content attribute: NOT_FOUND")

        # Debug session state
        logger.info(f"Session state 'state' key exists: {'state' in st.session_state}")
        logger.info(f"Session state 'optimization_in_progress' key: {st.session_state.get('optimization_in_progress', 'NOT_SET')}")

        # Debug current stage detection
        logger.info("Current stage detection logic:")
        logger.info(f"  - optimization_in_progress (state): {getattr(state, 'optimization_in_progress', 'NOT_SET')}")
        logger.info(f"  - optimization_in_progress (session): {st.session_state.get('optimization_in_progress', 'NOT_SET')}")
        logger.info(f"  - optimization_complete (state): {getattr(state, 'optimization_complete', 'NOT_SET')}")
        logger.info(f"  - combined_script_path (state): {getattr(state, 'combined_script_path', 'NOT_SET')}")

        logger.info("=" * 50)

        # Re-validate prerequisites before starting optimization (double-check)
        logger.info("Phase 8: Re-validating prerequisites before starting optimization")

        # Check Google API key with enhanced feedback
        if not hasattr(state, 'google_api_key') or not state.google_api_key:
            error_msg = "Google AI API key is required for script optimization"

            # Show prominent error message
            st.markdown("""
            <div style="text-align: center; margin: 20px 0; padding: 15px; background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%); border-radius: 12px; border: 2px solid #f44336; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <div style="font-size: 24px; margin-bottom: 8px;">🔑</div>
                <p style="font-size: 18px; color: #c62828; margin: 0; font-weight: 600;">Google AI API Key Required</p>
                <p style="font-size: 14px; color: #c62828; margin: 8px 0 0 0;">Please configure your Google AI API key in the sidebar to proceed.</p>
            </div>
            """, unsafe_allow_html=True)

            logger.error(f"Phase 8: VALIDATION FAILED - {error_msg}")
            logger.error(f"Phase 8: google_api_key attribute exists: {hasattr(state, 'google_api_key')}")
            logger.error(f"Phase 8: google_api_key value: {getattr(state, 'google_api_key', 'NOT_SET')}")
            return

        # Check combined script content with enhanced feedback
        if not hasattr(state, 'combined_script_content') or not state.combined_script_content:
            error_msg = "No combined script content found"

            # Show prominent error message
            st.markdown("""
            <div style="text-align: center; margin: 20px 0; padding: 15px; background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%); border-radius: 12px; border: 2px solid #ff9800; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <div style="font-size: 24px; margin-bottom: 8px;">📄</div>
                <p style="font-size: 18px; color: #e65100; margin: 0; font-weight: 600;">Combined Script Required</p>
                <p style="font-size: 14px; color: #e65100; margin: 8px 0 0 0;">Please complete Phase 7 first to generate the combined script.</p>
            </div>
            """, unsafe_allow_html=True)

            logger.error(f"Phase 8: VALIDATION FAILED - {error_msg}")
            logger.error(f"Phase 8: combined_script_content attribute exists: {hasattr(state, 'combined_script_content')}")
            content = getattr(state, 'combined_script_content', '')
            content_length = len(content) if content is not None else 0
            logger.error(f"Phase 8: combined_script_content length: {content_length}")
            return

        logger.info("Phase 8: All prerequisites validated successfully - starting optimization")

        try:
            # Initialize optimization start time
            start_time = datetime.now()
            state.optimization_start_time = start_time
            logger.info(f"Phase 8: Setting optimization start time to {start_time}")
            logger.info(f"Phase 8: Start time type: {type(start_time)}")

            # Set the optimization in progress flag in BOTH state object AND session state
            state.optimization_in_progress = True
            st.session_state['optimization_in_progress'] = True  # Add this for persistence
            logger.info("Phase 8: Set optimization_in_progress flag to True in both state and session_state")

            # Log current state before rerun
            logger.info(f"Phase 8: State before rerun - optimization_in_progress: {state.optimization_in_progress}")
            logger.info(f"Phase 8: State before rerun - optimization_start_time: {state.optimization_start_time}")
            logger.info(f"Phase 8: Session state before rerun - optimization_in_progress: {st.session_state.get('optimization_in_progress', 'NOT_SET')}")
            content_length = len(state.combined_script_content) if state.combined_script_content is not None else 0
            logger.info(f"Phase 8: State before rerun - combined_script_content length: {content_length}")

            # Force state update in session state
            st.session_state['state'] = state
            logger.info("Phase 8: Updated session state with new optimization flags")
            logger.info("Phase 8: Calling st.rerun() to start optimization process")
            st.rerun()

        except Exception as e:
            error_msg = f"Error starting optimization: {str(e)}"
            st.error(error_msg)
            logger.error(f"Phase 8: CRITICAL ERROR - {error_msg}", exc_info=True)
            logger.error(f"Phase 8: Exception type: {type(e)}")
            logger.error(f"Phase 8: Exception args: {e.args}")

            # Reset optimization state on error
            state.optimization_in_progress = False
            state.optimization_start_time = None

            # Clear the session state flag as well
            if 'optimization_in_progress' in st.session_state:
                del st.session_state['optimization_in_progress']
                logger.error("Phase 8: Cleared optimization_in_progress flag from session state due to error")

            st.session_state['state'] = state
            logger.error("Phase 8: Reset optimization state due to error")

    # Add a debug section to help diagnose button issues
    with st.expander("🔍 Debug Information (Stage 8 Button Troubleshooting)", expanded=False):
        st.markdown("**Current State Values:**")
        st.write(f"- optimization_complete: `{getattr(state, 'optimization_complete', 'NOT_SET')}`")
        st.write(f"- optimization_in_progress: `{getattr(state, 'optimization_in_progress', 'NOT_SET')}`")
        st.write(f"- optimized_script_path: `{getattr(state, 'optimized_script_path', 'NOT_SET')}`")
        # Safe length check for combined_script_content (handles None values)
        combined_content = getattr(state, 'combined_script_content', '')
        combined_content_length = len(combined_content) if combined_content is not None else 0
        st.write(f"- combined_script_content length: `{combined_content_length}`")
        st.write(f"- google_api_key present: `{bool(getattr(state, 'google_api_key', None))}`")

        st.markdown("**Session State Keys:**")
        st.write(f"- Session state keys: `{list(st.session_state.keys())}`")
        st.write(f"- optimization_in_progress in session: `{st.session_state.get('optimization_in_progress', 'NOT_SET')}`")

        st.markdown("**Button Visibility Test:**")
        if st.button("🧪 Test Button (Should work if Streamlit is functioning)", key="debug_test_button"):
            st.success("✅ Test button clicked! Streamlit button functionality is working.")
            logger.info("Stage 8: Debug test button clicked successfully")

        st.markdown("**Expected Behavior:**")
        st.info("If you can see this debug section but not the main optimization button, there may be a conditional rendering issue in the Stage 8 function.")

        # Force render the optimization button here as a backup
        st.markdown("**Backup Optimization Button:**")
        if st.button("🔄 Backup: Start Script Optimization", use_container_width=True, key="backup_optimization_button"):
            logger.info("=" * 80)
            logger.info("STAGE 8: USER CLICKED BACKUP OPTIMIZATION BUTTON")
            logger.info("=" * 80)

            # Same logic as main button
            st.balloons()
            st.success("✅ Backup button clicked! Starting optimization...")

            # Set optimization flags
            state.optimization_in_progress = True
            state.optimization_start_time = datetime.now()
            state.optimization_complete = False
            st.session_state['optimization_in_progress'] = True
            st.session_state['state'] = state

            logger.info("Stage 8: Backup button - set optimization flags and calling st.rerun()")
            st.rerun()


def _check_optimization_prerequisites(state):
    """
    Check if all prerequisites for script optimization are met.

    This function only checks for the final merged script from Stage 7 and the Google AI API key.

    Args:
        state (StateManager): The application state manager instance

    Returns:
        tuple: (prerequisites_met: bool, missing_items: list)
    """
    logger.info("Stage 8: Starting prerequisites validation for final script optimization")
    missing_items = []

    # Check Google AI API key
    logger.info("Stage 8: Checking Google AI API key prerequisite")
    if not hasattr(state, 'google_api_key') or not state.google_api_key:
        logger.warning("Stage 8: Google AI API key is missing or empty")
        missing_items.append('google_api_key')
    else:
        api_key_length = len(state.google_api_key)
        logger.info(f"Stage 8: Google AI API key is present (length: {api_key_length})")

    # Check for final script from Stage 7 (either path or content)
    logger.info("Stage 8: Checking final script from Stage 7")
    has_script_path = (hasattr(state, 'combined_script_path') and
                      state.combined_script_path and
                      os.path.exists(state.combined_script_path))
    has_script_content = (hasattr(state, 'combined_script_content') and
                         state.combined_script_content)

    if not has_script_path and not has_script_content:
        logger.warning("Stage 8: No final script available from Stage 7")
        missing_items.append('final_script')
    else:
        if has_script_path:
            logger.info(f"Stage 8: Final script path available: {state.combined_script_path}")
        if has_script_content:
            content_length = len(state.combined_script_content)
            logger.info(f"Stage 8: Final script content available (length: {content_length})")

    # Check selected test case
    logger.info("Stage 8: Checking selected test case prerequisite")
    if not hasattr(state, 'selected_test_case') or not state.selected_test_case:
        logger.warning("Stage 8: No test case selected")
        missing_items.append('selected_test_case')
    else:
        test_case_id = state.selected_test_case.get('Test Case ID', 'Unknown') if isinstance(state.selected_test_case, dict) else 'Invalid'
        logger.info(f"Stage 8: Selected test case: {test_case_id}")

    prerequisites_met = len(missing_items) == 0

    logger.info(f"Stage 8: Prerequisites validation complete")
    logger.info(f"Stage 8: Prerequisites met: {prerequisites_met}")
    logger.info(f"Stage 8: Missing items: {missing_items}")

    return prerequisites_met, missing_items


def _show_prerequisites_guidance(state, missing_items):
    """
    Show user-friendly guidance for missing prerequisites.

    Args:
        state (StateManager): The application state manager instance
        missing_items (list): List of missing prerequisite items
    """
    logger.info(f"Stage 8: Displaying prerequisites guidance for {len(missing_items)} missing items: {missing_items}")
    st.markdown("### ⚠️ Prerequisites Required")

    # Create a prominent warning box
    st.markdown("""
    <div style="padding: 20px; background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
                border-radius: 12px; border: 2px solid #ffc107; margin: 20px 0;">
        <div style="text-align: center; margin-bottom: 15px;">
            <div style="font-size: 48px; margin-bottom: 10px;">⚠️</div>
            <h3 style="color: #856404; margin: 0;">Setup Required Before Optimization</h3>
            <p style="color: #856404; margin: 5px 0 0 0;">Please complete the following steps to proceed with script optimization.</p>
        </div>
    </div>
    """, unsafe_allow_html=True)

    # Show specific guidance for each missing item
    st.markdown("#### 📋 Required Setup Steps:")

    if 'google_api_key' in missing_items:
        with st.expander("🔑 Configure Google AI API Key", expanded=True):
            st.error("**Missing: Google AI API Key**")
            st.markdown("""
            **How to fix:**
            1. Look for the **Google AI API Key** field in the sidebar
            2. Enter your Google AI Studio API key (starts with 'AIza...')
            3. If you don't have a key, get one from [Google AI Studio](https://aistudio.google.com/)

            **Why needed:** The AI optimization requires access to Google's Gemini model to analyze and improve your test script.
            """)

    if 'final_script' in missing_items:
        with st.expander("📄 Complete Script Generation (Stage 7)", expanded=True):
            st.error("**Missing: Final Merged Script from Stage 7**")
            st.markdown("""
            **How to fix:**
            1. Go back to **Stage 7 (Script Execution)**
            2. Complete all test steps for your selected test case
            3. Execute all individual step scripts
            4. Ensure the final merged script is created
            5. Return to Stage 8 for optimization

            **Why needed:** Stage 8 optimizes the final merged script from Stage 7. This script contains all test steps merged into a single executable file.
            """)

    if 'selected_test_case' in missing_items:
        with st.expander("🎯 Select Test Case (Stage 3)", expanded=True):
            st.error("**Missing: Selected Test Case**")
            st.markdown("""
            **How to fix:**
            1. Go back to **Stage 3 (Test Case Selection)**
            2. Select a test case from your uploaded CSV file
            3. Complete the workflow through Stages 4-7

            **Why needed:** The optimization process needs to know which test case is being optimized for proper file naming and context.
            """)

    # Show current state for debugging
    with st.expander("🔍 Current State (Debug Info)", expanded=False):
        st.markdown("**Current State Information:**")

        # API Key status
        api_key_status = "✅ SET" if hasattr(state, 'google_api_key') and state.google_api_key else "❌ NOT SET"
        st.markdown(f"- Google AI API Key: {api_key_status}")

        # Final script status
        has_script_path = (hasattr(state, 'combined_script_path') and
                          state.combined_script_path and
                          os.path.exists(state.combined_script_path))
        has_script_content = (hasattr(state, 'combined_script_content') and
                             state.combined_script_content)

        if has_script_path or has_script_content:
            if has_script_content:
                script_length = len(state.combined_script_content)
                st.markdown(f"- Final Script from Stage 7: ✅ SET ({script_length:,} characters)")
            if has_script_path:
                st.markdown(f"- Final Script Path: ✅ SET ({state.combined_script_path})")
        else:
            st.markdown("- Final Script from Stage 7: ❌ NOT SET")

        # Test case status
        if hasattr(state, 'selected_test_case') and state.selected_test_case:
            test_case_id = state.selected_test_case.get('Test Case ID', 'Unknown')
            st.markdown(f"- Selected Test Case: ✅ SET ({test_case_id})")
        else:
            st.markdown("- Selected Test Case: ❌ NOT SET")

    # Navigation guidance
    st.markdown("### 🧭 Quick Navigation")

    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("🔑 Configure API Key", use_container_width=True, help="Set up Google AI API key in sidebar"):
            st.info("👈 Look for the Google AI API Key field in the sidebar")

    with col2:
        if st.button("📄 Go to Stage 7", use_container_width=True, help="Complete script generation"):
            # Set navigation flag to go to Stage 7
            st.session_state['force_stage'] = 7
            st.session_state['stage_navigation_message'] = "Navigating to Stage 7 to complete script generation"
            logger.info("Stage 8: User requested navigation to Stage 7")
            st.rerun()

    with col3:
        if st.button("🎯 Go to Stage 3", use_container_width=True, help="Select a different test case"):
            # Reset state and go to Stage 3
            state.reset_test_case_state(confirm=True, reason="User requested navigation to Stage 3 from Stage 8")
            st.session_state['force_stage'] = 3
            st.session_state['stage_navigation_message'] = "Navigating to Stage 3 for test case selection"
            logger.info("Stage 8: User requested navigation to Stage 3")
            st.rerun()

    logger.info(f"Stage 8: Displayed prerequisites guidance for missing items: {missing_items}")

    # Final debug log at function exit
    _log_script_state_debug(state, "STAGE_8_FUNCTION_EXIT", _debug_log_path)
